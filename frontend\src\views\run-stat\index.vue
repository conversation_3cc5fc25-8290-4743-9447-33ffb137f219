<template>
  <div class="switch-container" :class="gridClass">
    <div
      v-for="item in switches"
      :key="item.id"
      class="switch-item"
      :class="{ disabled: item.disabled }"
      @click="handleClick(item)"
    >
      <v-icon size="x-large" class="switch-icon">{{ item.icon }}</v-icon>
      <div class="switch-label">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();

// 示例数据，实际使用时可以通过props传入
const switches = ref([
  {
    id: 1,
    label: "网络状态",
    icon: "mdi-wifi",
    path: "/network",
  },
  {
    id: 2,
    label: "效率统计",
    icon: "mdi-refresh",
    path: "/normal",
  },
]);

// 根据开关数量计算网格布局类名
const gridClass = computed(() => {
  const count = switches.value.length;
  switch (count) {
    case 1:
      return "grid-one";
    case 2:
      return "grid-two";
    case 3:
      return "grid-three";
    case 4:
      return "grid-four";
    case 5:
      return "grid-five";
    case 6:
      return "grid-six";
    default:
      return "grid-one";
  }
});

const handleClick = (item) => {
  const { path } = item;
  if (path) {
    router.push(path);
  }
};
</script>

<style scoped>
.switch-container {
  height: calc(100vh - var(--app-bar-height));
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: grid;
  gap: 30px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.switch-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background-color: rgb(var(--v-theme-surface));
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  min-height: 300px;
  min-width: 200px;
}

.switch-item:hover {
  transform: scale(1.02);
  background-color: rgb(var(--v-theme-primary), 0.7);
  color: white;
}

.switch-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.switch-label {
  font-size: 30px;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}

.switch-icon {
  font-size: 56px;
  margin-bottom: 16px;
}

/* 一个开关时居中 */
.grid-one {
  grid-template-columns: 1fr;
  justify-items: center;
  align-items: center;
  & .switch-item {
    width: 400px;
  }
}

/* 两个开关时左右排列 */
.grid-two {
  grid-template-columns: 1fr 1fr;
}

/* 三个开关时左中右排列 */
.grid-three {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 四个开关时2x2排列 */
.grid-four {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 五个开关时上面三个下面两个 */
.grid-five {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 六个开关时3x2排列 */
.grid-six {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}
</style>
