<template>
  <div class="home-container">
    <v-row class="fill-height" justify="center">
      <v-col
        v-for="item in menuItems"
        :key="item.path"
        cols="4"
        class="pa-2 flex justify-center items-center"
      >
        <v-card
          class="feature-card"
          elevation="2"
          @click="() => handleCardClick(item.path)"
          hover
          color="primary"
          variant="outlined"
        >
          <v-card-title class="text-center d-flex flex-column align-center justify-center gap-4">
            <v-icon size="48">{{ item.icon }}</v-icon>
            <span class="card-text">{{ item.title }}</span>
          </v-card-title>
        </v-card>
      </v-col>
      <v-col v-for="item in 3 - (menuItems.length % 3)" />
    </v-row>
    <v-snackbar v-model="showResetSnackbar" color="warning" location="top" :timeout="2000">
      系统即将重启，重新加载配置...
    </v-snackbar>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref } from "vue";
import { ipc } from "@/utils/ipcRenderer";

const router = useRouter();
const showResetSnackbar = ref(false);

const menuItems = ref([
  { path: "/debug/play", icon: "mdi-play", title: "画面播放" },
  { path: "/debug/mqtt", icon: "mdi-connection", title: "MQTT调试" },
  { path: "/debug/network", icon: "mdi-lan", title: "网络调试" },
  { path: "/debug/params", icon: "mdi-cog", title: "参数设置" },
  { path: "/debug/button", icon: "mdi-gesture-tap-button", title: "按钮配置" },
  { path: "/debug/log", icon: "mdi-notebook", title: "日志查看" },
  { path: "/debug/screen", icon: "mdi-monitor", title: "屏幕信息" },
  { path: "/debug/ctrl", icon: "mdi-monitor-edit", title: "操作台设置" },
  { path: "/debug/memory", icon: "mdi-memory", title: "内存监控" },
  { path: "/debug/storage", icon: "mdi-harddisk", title: "存储设备" },
  { path: "/", icon: "mdi-home", title: "返回首页" },
  // { path: "/reset", icon: "mdi-restart", title: "软重启" },
  { path: "/exit", icon: "mdi-exit-to-app", title: "退出系统" },
]);

const handleClose = () => {
  ipc.invoke("controller/example/quit");
};

const handleCardClick = (path) => {
  if (path === "/exit") {
    handleClose();
  } else if (path === "/reset") {
    // 软重启重新获取相关参数，检查配置，提示警告信息
    showResetSnackbar.value = true;
  } else {
    setTimeout(() => router.push(path), 300);
  }
};
</script>

<style scoped>
.home-container {
  width: 1280px;
  height: 800px;
  margin: 0 auto;
  padding: 20px 40px;
  background: url("../../assets/image/skin-bg.gif") repeat;
  display: flex;
  align-items: center;
}

.feature-card {
  width: 290px;
  height: 150px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.feature-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
}

.text-center {
  width: 100%;
}

.card-text {
  font-size: 18px;
  font-weight: 500;
  margin-top: 12px;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}
</style>
