# U盘检测和文件下载目标选择功能演示

## 功能概述

本演示实现了完整的U盘检测和文件下载目标选择功能，包括：

1. **实时U盘检测**：自动检测U盘插入和拔出事件
2. **存储设备管理**：获取所有可用存储设备信息
3. **设备选择界面**：用户友好的设备选择对话框
4. **文件下载演示**：集成设备选择的表格数据导出功能

## 技术架构

### 主进程组件

1. **StorageDeviceService** (`electron/service/storageDeviceService.js`)
   - 单例模式的存储设备检测服务
   - 支持Windows和Linux系统
   - 实时监控设备插入/拔出事件
   - 提供设备信息查询和格式化功能

2. **StorageController** (`electron/controller/storage.js`)
   - 提供IPC接口供前端调用
   - 处理设备列表获取、监控控制等操作
   - 向渲染进程发送设备状态变化事件

### 前端组件

1. **DeviceSelectionDialog** (`frontend/src/components/DeviceSelectionDialog/index.vue`)
   - 设备选择对话框组件
   - 显示设备详细信息（容量、可用空间、类型等）
   - 实时更新设备状态
   - 支持设备可写性检查

2. **TableDownloadDemo** (`frontend/src/components/TableDownloadDemo/index.vue`)
   - 表格数据导出演示组件
   - 集成设备选择功能
   - 支持多种导出格式（CSV、Excel、JSON、TXT）
   - 模拟导出进度显示

3. **StorageDemo** (`frontend/src/views/debug/storage/index.vue`)
   - 主演示页面
   - 设备列表展示
   - 设备详情查看
   - 监控状态控制

## 使用方法

### 1. 启动应用

```bash
npm run dev
```

### 2. 访问演示页面

1. 启动应用后，进入调试模式
2. 点击"存储设备"卡片
3. 或直接访问路由：`#/debug/storage`

### 3. 功能演示

#### 设备检测
- 插入U盘后会自动检测并显示在设备列表中
- 拔出U盘后会自动从列表中移除
- 实时显示设备状态（可写/只读、容量信息等）

#### 文件下载
1. 点击"生成示例数据"按钮生成测试数据
2. 选择要导出的数据行（可多选）
3. 选择导出格式和文件名
4. 点击"导出到设备"按钮
5. 在弹出的设备选择对话框中选择目标设备
6. 确认后开始模拟导出过程

## 支持的平台

### Windows
- 使用`wmic`命令获取逻辑磁盘信息
- 支持检测硬盘分区和可移动设备
- 自动识别设备类型（固定磁盘、可移动磁盘、CD-ROM等）

### Linux
- 使用`df`命令获取挂载的文件系统信息
- 过滤系统和虚拟文件系统
- 支持检测挂载在`/media`、`/mnt`等目录的设备

## API接口

### IPC接口

```javascript
// 获取设备列表
await ipc.invoke('controller/storage/getDevices', options)

// 获取设备详细信息
await ipc.invoke('controller/storage/getDeviceInfo', { deviceId })

// 检查设备可写性
await ipc.invoke('controller/storage/checkDeviceWritable', { mountPoint })

// 启动/停止监控
await ipc.invoke('controller/storage/startMonitoring')
await ipc.invoke('controller/storage/stopMonitoring')

// 获取监控状态
await ipc.invoke('controller/storage/getMonitoringStatus')

// 刷新设备列表
await ipc.invoke('controller/storage/refreshDevices')
```

### 事件监听

```javascript
// 设备连接事件
ipc.on('storage:device-connected', (event, device) => {
  console.log('设备已连接:', device)
})

// 设备断开事件
ipc.on('storage:device-disconnected', (event, device) => {
  console.log('设备已断开:', device)
})

// 设备列表更新事件
ipc.on('storage:devices-updated', (event, devices) => {
  console.log('设备列表已更新:', devices)
})
```

## 设备信息结构

```javascript
{
  id: "C:",                    // 设备唯一标识
  label: "C:",                 // 设备标签
  mountPoint: "C:",            // 挂载点
  type: "fixed",               // 设备类型
  totalSize: 1000000000,       // 总容量（字节）
  freeSpace: 500000000,        // 可用空间（字节）
  usedSpace: 500000000,        // 已用空间（字节）
  volumeName: "Windows",       // 卷标名称
  isRemovable: false,          // 是否为可移动设备
  isWritable: true,            // 是否可写
  platform: "win32",          // 平台类型
  filesystem: "NTFS"           // 文件系统类型（Linux）
}
```

## 错误处理

- 设备扫描失败时会显示错误信息
- 不可写设备会被标记并禁用选择
- 网络错误和权限问题会有相应提示
- 提供重试机制

## 扩展功能

### 可以进一步扩展的功能：

1. **文件实际写入**：当前为模拟导出，可扩展为真实文件写入
2. **进度回调**：实现真实的文件写入进度监控
3. **文件格式支持**：添加更多导出格式支持
4. **设备过滤**：根据容量、类型等条件过滤设备
5. **安全检查**：添加设备安全扫描功能
6. **批量操作**：支持批量文件操作

## 注意事项

1. 需要相应的系统权限来访问存储设备
2. Linux系统可能需要额外的挂载权限
3. 某些设备可能需要特殊的驱动程序
4. 建议在实际使用前进行充分测试

## 故障排除

### 常见问题

1. **设备检测不到**
   - 检查设备是否正确连接
   - 确认设备驱动是否安装
   - 检查应用权限

2. **设备显示为只读**
   - 检查设备是否有写保护
   - 确认文件系统权限
   - 尝试以管理员权限运行

3. **监控不工作**
   - 检查系统命令是否可用（wmic、df）
   - 确认防火墙设置
   - 查看控制台错误信息
