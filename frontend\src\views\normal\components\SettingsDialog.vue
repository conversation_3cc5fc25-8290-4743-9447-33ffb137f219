<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="900px"
    z-index="900"
  >
    <v-card>
      <v-card-title class="headline">设置</v-card-title>
      <v-tabs v-model="currentTab" background-color="#333333" dark>
        <v-tab>常规设置</v-tab>
        <v-tab>高级设置</v-tab>
      </v-tabs>
      <v-window v-model="currentTab">
        <v-window-item :value="0">
          <div class="mx-6 my-2">
            <v-row class="!m-0 flex justify-between items-center mb-2">
              <div class="text-lg font-bold">班组设置</div>
              <v-btn color="surface" :outlined="true" @click="addGroup">添加班组</v-btn>
            </v-row>
            <v-row v-for="(group, index) in localConfigData.group" :key="index" align="center">
              <v-col cols="3">
                <Keyboard v-model="group.name" label="班组名称" hide-details></Keyboard>
              </v-col>
              <v-col cols="3">
                <Keyboard v-model="group.driver_name" label="司机名称" hide-details></Keyboard>
              </v-col>
              <v-col cols="2">
                <v-text-field v-model="group.start_time" label="开始时间" type="time" hide-details></v-text-field>
              </v-col>
              <v-col cols="2">
                <v-text-field v-model="group.end_time" label="结束时间" type="time" hide-details></v-text-field>
              </v-col>
              <v-col cols="2">
                <v-btn icon="mdi-delete" color="error" variant="text" @click="removeGroup(index)"></v-btn>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
        <v-window-item :value="1">
          <div class="mx-6 my-2">
            <!-- 高级设置内容 -->
            <!-- <v-row class="!m-0 flex items-center">
              <div class="text-lg font-bold mr-4">模式切换</div>
              <v-radio-group v-model="localConfigData.mode" inline hide-details>
                <v-radio label="简易模式" value="simple"></v-radio>
                <v-radio label="记斗模式" value="complex"></v-radio>
              </v-radio-group>
            </v-row>
            <v-divider class="my-4"></v-divider> -->
            <v-row class="!m-0 flex items-center">
              <div class="text-lg font-bold mr-4">车辆名称</div>
              <Keyboard
                v-model="localConfigData.vehicle_name"
                density="compact"
                variant="outlined"
                type="text"
                hide-details
              ></Keyboard>
            </v-row>
            <!-- <v-divider class="my-4"></v-divider> -->

            <v-row class="!m-0 mt-4 flex items-center">
              <div class="text-lg font-bold mr-4">装车阈值</div>
              <Keyboard
                v-model="localConfigData.load_threshold"
                density="compact"
                variant="outlined"
                type="number"
                suffix="分钟"
                hide-details
              ></Keyboard>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" @click="save">保存</v-btn>
        <v-btn color="primary" @click="$emit('update:modelValue', false)">关闭</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, reactive } from "vue";

const currentTab = ref(0);
import Keyboard from "@/components/Keyboard/index.vue";

const props = defineProps({
  modelValue: Boolean,
  configData: Object,
});

const emit = defineEmits(["update:modelValue", "save"]);

const localConfigData = reactive(JSON.parse(JSON.stringify(props.configData)));

watch(
  () => props.configData,
  (newVal) => {
    Object.assign(localConfigData, JSON.parse(JSON.stringify(newVal)));
  },
  { deep: true }
);

const addGroup = () => {
  localConfigData.group.push({
    name: `新班组${localConfigData.group.length + 1}`,
    driver_name: "",
    start_time: "00:00",
    end_time: "00:00",
  });
};

const removeGroup = (index) => {
  localConfigData.group.splice(index, 1);
};

const save = () => {
  emit("save", localConfigData);
};
</script>
