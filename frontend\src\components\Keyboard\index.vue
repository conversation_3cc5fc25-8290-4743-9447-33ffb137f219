<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from "vue";
import Keyboard from "simple-keyboard";
import "simple-keyboard/build/css/index.css";
import englishLayout from "./english";
import japaneseLayout from "./japanese";
import chineseLayout from "./chinese";

const props = defineProps({
  modelValue: [String, Number],
});

const emit = defineEmits(["update:modelValue"]);

const isKeyboardVisible = ref(false);
const keyboard = ref(null);
const keyboardContainer = ref(null);
const inputRef = ref(null);
const currentLanguage = ref("chinese");
const caretPosition = ref(0); // 跟踪光标位置

// 添加全局点击事件处理
const handleClickOutside = (event) => {
  const keyboardElement = keyboardContainer.value;
  const inputElement = inputRef.value?.$el;

  // 检查点击是否在键盘或输入框范围内
  const isInsideKeyboard = keyboardElement?.contains(event.target);
  const isInsideInput = inputElement?.contains(event.target);

  if (!isInsideKeyboard && !isInsideInput) {
    hideKeyboard();
  }
};

onMounted(async () => {
  await nextTick();

  keyboard.value = new Keyboard(keyboardContainer.value, {
    onChange: (input) => {
      // 将当前值转换为字符串以便处理
      const oldValue = props.modelValue !== undefined && props.modelValue !== null ? String(props.modelValue) : "";
      const newValue = input;

      if (newValue.length < oldValue.length) {
        caretPosition.value = newValue.length;
      } else if (oldValue.length === 0) {
        caretPosition.value = 1;
      } else {
        outerLoop: for (let i = 0; i < newValue.length; i++) {
          const newString = newValue[i];
          for (let j = 0; j < oldValue.length; j++) {
            const oldString = oldValue[j];
            if (i === j && newString !== oldString) {
              caretPosition.value = i + 1;
              break outerLoop;
            } else {
              caretPosition.value = oldValue.length + 1;
            }
          }
        }
      }

      // 如果原始值是数字类型，尝试将新值转换为数字
      if (typeof props.modelValue === "number" && !isNaN(Number(input))) {
        emit("update:modelValue", Number(input));
      } else {
        emit("update:modelValue", input);
      }

      keyboard.value.setCaretPosition(caretPosition.value);
    },
    onKeyPress: (button) => {
      // console.log("Button pressed", button);
      if (button === "{enter}") hideKeyboard();
      if (button === "{shift}" || button === "{lock}") handleShift();
      if (button === "{lang}") handleLanguageSwitch();
    },
    ...getKeyboardLayout(),
  });

  hideKeyboard();
});

// 清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener("mousedown", handleClickOutside);
});

const handleFocus = () => {
  showKeyboard();
  inputRef.value?.focus();
};

// 移除原有的blur处理
const showKeyboard = () => {
  if (!isKeyboardVisible.value) {
    isKeyboardVisible.value = true;
    // 确保数字类型的值被转换为字符串
    const inputValue = props.modelValue !== undefined && props.modelValue !== null ? String(props.modelValue) : "";
    keyboard.value.setInput(inputValue);
    // 添加全局点击监听
    document.addEventListener("mousedown", handleClickOutside);
  }
};

const hideKeyboard = () => {
  if (isKeyboardVisible.value) {
    isKeyboardVisible.value = false;
    // 移除全局点击监听
    document.removeEventListener("mousedown", handleClickOutside);
    inputRef.value?.blur();
  }
};

const handleShift = () => {
  const currentLayout = keyboard.value.options.layoutName;
  const shiftToggle = currentLayout === "default" ? "shift" : "default";
  keyboard.value.setOptions({
    layoutName: shiftToggle,
  });
};

const getKeyboardLayout = () => {
  const baseLayout = (() => {
    switch (currentLanguage.value) {
      case "english":
        return englishLayout;
      case "japanese":
        return japaneseLayout;
      default:
        return chineseLayout;
    }
  })();

  if (currentLanguage.value !== "chinese") {
    baseLayout.layoutCandidates = {};
  }
  return baseLayout;
};

const handleLanguageSwitch = () => {
  const languages = ["chinese", "english", "japanese"];
  const currentIndex = languages.indexOf(currentLanguage.value);
  const nextIndex = (currentIndex + 1) % languages.length;
  currentLanguage.value = languages[nextIndex];

  keyboard.value.setOptions({
    ...getKeyboardLayout(),
    layoutName: "default",
  });
};

// 更新光标位置的方法
const updateCaretPosition = () => {
  if (!inputRef.value || !keyboard.value) return;
  const input = inputRef.value.$el.querySelector("input");
  if (input) {
    caretPosition.value = input.selectionStart;
    keyboard.value.setCaretPosition(caretPosition.value);
  }
};
</script>

<template>
  <v-text-field
    ref="inputRef"
    :model-value="modelValue"
    @focus="handleFocus"
    @click="updateCaretPosition"
    @input="
      (e) => {
        // 如果原始值是数字类型，尝试将新值转换为数字
        if (typeof modelValue === 'number' && !isNaN(Number(e.target.value))) {
          $emit('update:modelValue', Number(e.target.value));
        } else {
          $emit('update:modelValue', e.target.value);
        }
      }
    "
    v-bind="$attrs"
  />
  <Teleport to="body">
    <div
      ref="keyboardContainer"
      class="simple-keyboard"
      :style="{ display: isKeyboardVisible ? 'block' : 'none' }"
    ></div>
  </Teleport>
</template>

<style>
.simple-keyboard {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 8px;
  background: rgb(var(--v-theme-surface));
  transition: transform 0.3s ease;
  overflow: visible !important; /* 确保内容可以溢出显示 */
}

.simple-keyboard.hg-theme-default {
  background: rgb(var(--v-theme-surface));
  overflow: visible !important;
}

.simple-keyboard.hg-theme-default .hg-button {
  color: rgb(var(--v-theme-on-surface)) !important;
  background-color: rgb(var(--v-theme-background)) !important;
}
.simple-keyboard.hg-theme-default .hg-button:active {
  background-color: rgb(var(--v-theme-on-code)) !important;
}

/* 候选字面板样式 */
.hg-candidate-box {
  background: rgb(var(--v-theme-surface));
  border: 1px solid rgba(var(--v-theme-on-surface), 0.12);
}
</style>
