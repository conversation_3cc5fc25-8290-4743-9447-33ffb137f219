import { get, post, put, del } from "./index";

export const getLayoutList = (vehicleID) => {
  return get(
    `/vehicles/${vehicleID}/service/mix_template`,
    {},
    { headers: { authorization: "Bearer t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb" } }
  );
};

export const setLayout = (vehicleID, layout) => {
  return put(`/vehicles/${vehicleID}/service/mix_layout`, layout, {
    headers: { authorization: "Bearer t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb" },
  });
};

// 事件上报
export const reportEventApi = (event) => {
  return post(`prod_count/events`, event, {
    headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
  });
};

// 获取车辆配置
export const getVehicleConfigApi = (vehicleID) => {
  return get(
    `/prod_count/config/${vehicleID}`,
    {},
    {
      headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
    }
  );
};

// 配置车辆
export const setVehicleConfigApi = (vehicleID, config) => {
  return put(`/prod_count/config/${vehicleID}`, config, {
    headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
  });
};

// 历史装车记录
export const getHistoryLoadApi = (vehicleID) => {
  return get(
    `/prod_count/history_load`,
    { vehicle_id: vehicleID, page_size: 50, page_no: 1 },
    {
      headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
    }
  );
};

// 最近班组记录
export const getRecentTeamApi = (vehicleID) => {
  return get(
    `/prod_count/recent_day_team/${vehicleID}`,
    {},
    {
      headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
    }
  );
};

// 历史班组记录
export const getHistoryTeamApi = (vehicleID) => {
  return get(
    `/prod_count/history_team`,
    { vehicle_id: vehicleID, page_size: 50, page_no: 1 },
    {
      headers: { authorization: "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a" },
    }
  );
};

// 排行榜查询
// 历史装车最快班次
// 历史装车最多次数
