"use strict";
const globalStateManager = require("../service/globalStateManager");
const { calculateCrc16 } = require("./crc");

class MessageEncoder {
  // 静态属性，用于存储不同ID的计数
  static transmitCounters = new Map();

  /**
   * 获取传输计数
   * @param {string|number} id - 标识符
   * @returns {number} 1-256范围内的计数值
   */
  static getTransmitCount(id) {
    if (!this.transmitCounters.has(id)) {
      this.transmitCounters.set(id, 0);
    }

    let count = this.transmitCounters.get(id);
    count = (count % 256) + 1; // 确保值在1-256范围内
    this.transmitCounters.set(id, count);

    return count;
  }

  /**
   * 构建消息头
   * @param {object} headData - 消息头数据
   * @returns {Uint8Array} 编码后的消息头
   */
  static buildHead(headData = {}) {
    const headLength = 20; // 头部固定长度
    const buffer = new ArrayBuffer(headLength);
    const dataView = new DataView(buffer);
    let offset = 0;

    // 特征位固定值 (2字节)
    dataView.setUint8(offset, 0x0c);
    dataView.setUint8(offset + 1, 0x10);
    offset += 2;

    // 传输协议版本 (1字节)
    dataView.setUint8(offset, headData.version || 0x30);
    offset += 1;

    // 车辆类型标识 (1字节)
    dataView.setUint8(offset, headData.vehicleType || 0);
    offset += 1;

    // 头长度 (2字节)
    dataView.setUint16(offset, headLength, true);
    offset += 2;

    /**
     * 目标地址和源地址定义(1字节):
     *
     * 服务器相关:
     * - 0: 服务器
     *
     * 操作台设备:
     * - 1: 操作台采集板
     * - 2: 操作台工控机
     * - 3: 操作台平板电脑
     * - 11-14: 操作台按钮板(1-4级)
     *
     * 智控设备:
     * - 128: 智控箱主控板
     * - 129: 智控箱工控机
     * - 130: ros中core_node代码
     * - 135: 车端智能化软件
     *
     * 其他设备:
     * - 151: 驱动盒核心板
     * - 161: 电磁阀控制板
     * - 200: 驾驶室平板电脑
     * - 255: 广播地址
     */
    dataView.setUint8(offset, headData.targetAddress || 2);
    offset += 1;

    // 源地址 (1字节)
    dataView.setUint8(offset, 3);
    offset += 1;

    // 传输计数 (1字节)
    // 单独ID自增
    dataView.setUint8(offset, headData.transmitCount || 1);
    offset += 1;

    // 数据单元ID个数 (1字节)
    // 一般为1，也可以一次发多个数据
    dataView.setUint8(offset, headData.dataUnitCount || 1);
    offset += 1;

    // 数据单元长度 (2字节) 数据体长度
    dataView.setUint16(offset, headData.dataUnitLength || 12, true);
    offset += 2;

    // 时间戳 (8字节)
    const timestamp = BigInt(Date.now());
    dataView.setBigUint64(offset, timestamp, true);

    return new Uint8Array(buffer);
  }

  static async buildBody(id, bodyData = {}) {
    const { encoderConfig } = require("./messageDict");
    const configKey = `ID${id}`;

    if (!encoderConfig[configKey]) {
      throw new Error(`ID ${id} 未找到对应的配置`);
    }

    const config = encoderConfig[configKey];
    let bodyLength = config[1].value;
    if (id === 20) {
      // 对于 ID20，需要计算 JSON 数据的实际长度
      const jsonString = JSON.stringify(bodyData.jsonData || {});
      const jsonBytes = new TextEncoder().encode(jsonString);

      // 基础字段长度（id + dataLength + communicationType + parameterPackageType + reserved）+ JSON 数据长度
      bodyLength = 8 + jsonBytes.length;
    }
    const buffer = new ArrayBuffer(bodyLength);
    const dataView = new DataView(buffer);
    let offset = 0;

    // 验证传入的参数是否都在配置中定义
    const validKeys = config.map((field) => field.key);
    const invalidKeys = Object.keys(bodyData).filter((key) => !validKeys.includes(key));
    if (invalidKeys.length > 0) {
      throw new Error(`无效的参数: ${invalidKeys.join(", ")}`);
    }

    // 处理每个字段
    for (const field of config) {
      const value = bodyData.hasOwnProperty(field.key) ? bodyData[field.key] : field.value;

      // 特殊字段处理
      if (field.key === "otherInfo") {
        const arr = Array.isArray(value) ? value.slice(0, 3) : [0, 0, 0];
        for (let i = 0; i < 3; i++) {
          dataView.setUint8(offset + i, arr[i] || 0);
        }
        offset += 3;
        continue;
      } else if (field.key === "extendedGeneralFeatures") {
        // 获取按钮列表
        // 使用 await 需要确保在 async 函数内部
        // 这里需要将包含此代码的函数标记为 async
        const btnList = (await globalStateManager.get("pad.btnList")) || [];

        // 处理前3个字节，每2位表示一个按钮状态
        // 00: 未初始化, 01: 断开, 10: 接通, 11: 不适用或已失效
        const buttonBytes = [0, 0, 0]; // 初始化3个字节，默认所有位置为00(未初始化)

        // 筛选出车端按钮(type=1)并处理
        const vehicleButtons = btnList.filter((item) => item.type === 1);

        // 处理按钮，根据button.index确定位置
        for (const button of vehicleButtons) {
          // 确保index是数字，如果是字符串则转换
          const buttonIndex = parseInt(button.index, 10);

          // 检查index是否有效（0-11范围内）
          if (isNaN(buttonIndex) || buttonIndex < 0 || buttonIndex > 11) {
            continue; // 跳过无效的index
          }

          const byteIndex = Math.floor(buttonIndex / 4); // 确定在哪个字节
          const bitPosition = (buttonIndex % 4) * 2; // 确定在字节中的位置

          // 设置按钮状态: 10(接通) 或 01(断开)
          const buttonState = button.value ? 0b10 : 0b01;

          // 清除该位置原有的状态（将对应的2位设为00）
          buttonBytes[byteIndex] &= ~(0b11 << (6 - bitPosition));

          // 将新状态写入对应字节的对应位置（高位对应低索引值）
          buttonBytes[byteIndex] |= buttonState << (6 - bitPosition);
        }

        // 写入按钮状态(前3个字节)
        for (let i = 0; i < 3; i++) {
          dataView.setUint8(offset + i, buttonBytes[i]);
        }

        // 第4个字节: 当前存在的触摸点数量(默认为0)
        const touchCount = 0; // 这里可以根据实际情况设置触摸点数量
        dataView.setUint8(offset + 3, touchCount);

        // 第5~24字节: 每4个字节表示一个触摸位置(2字节横坐标，2字节纵坐标)
        // 初始化所有触摸点坐标为0
        for (let i = 0; i < 5; i++) {
          const touchPointOffset = offset + 4 + i * 4;
          dataView.setUint16(touchPointOffset, 0, true); // x坐标
          dataView.setUint16(touchPointOffset + 2, 0, true); // y坐标
        }

        // 更新offset
        offset += 24;
        continue;
      } else if (field.key === "extendedSafetyFeatures") {
        // 扩展安全功能的2个字节填写0xAA
        // 这是为了在极端条件下依然保证车辆控制安全
        dataView.setUint8(offset, 0xaa);
        dataView.setUint8(offset + 1, 0xaa);
        offset += 2;
        continue;
      } else if (field.key === "jsonData") {
        // 将 JSON 数据转换为字符串并编码为 UTF-8 字节数组
        const jsonString = JSON.stringify(value || {});
        const encoder = new TextEncoder("utf-8");
        const jsonBytes = encoder.encode(jsonString);

        // 将 JSON 数据写入缓冲区
        for (let i = 0; i < jsonBytes.length; i++) {
          dataView.setUint8(offset + i, jsonBytes[i]);
        }
        offset += jsonBytes.length;
        continue;
      } else if (field.key === "dataLength") {
        dataView.setUint16(offset, bodyLength, !field.isBigEndian);
        offset += 2;
        continue;
      }

      // 标准字段处理
      switch (field.len) {
        case 1:
          dataView.setUint8(offset, value);
          break;
        case 2:
          dataView.setUint16(offset, value, !field.isBigEndian);
          break;
        case 4:
          dataView.setUint32(offset, value, !field.isBigEndian);
          break;
        case 8:
          if (typeof value === "bigint") {
            dataView.setBigUint64(offset, value, !field.isBigEndian);
          } else {
            dataView.setBigUint64(offset, BigInt(value), !field.isBigEndian);
          }
          break;
        default:
          throw new Error(`不支持的字段长度: ${field.len}`);
      }
      offset += field.len;
    }
    // console.log("body", buffer);

    return new Uint8Array(buffer);
  }

  /**
   * 将Uint8Array转换为十六进制字符串
   * @param {Uint8Array} data - 要转换的数据
   * @returns {string} 十六进制字符串
   */
  static toHexString(data) {
    return Array.from(data)
      .map((byte) => byte.toString(16).padStart(2, "0"))
      .join("");
  }
  /**
   * USB数据转义（用于发送）
   * @param {Buffer} data - 原始数据
   * @returns {Buffer} 转义后的数据
   */
  static escapeUsbData(data) {
    const result = [];
    const ESCAPE_CHAR = 0x7c;

    for (let i = 0; i < data.length; i++) {
      const byte = data[i];
      switch (byte) {
        case 0x7e: // 0x7E -> 0x7C 0x4E
          result.push(ESCAPE_CHAR, 0x4e);
          break;
        case 0x7d: // 0x7D -> 0x7C 0x4D
          result.push(ESCAPE_CHAR, 0x4d);
          break;
        case 0x7c: // 0x7C -> 0x7C 0x4C
          result.push(ESCAPE_CHAR, 0x4c);
          break;
        default:
          result.push(byte);
          break;
      }
    }

    return Buffer.from(result);
  }


  /**
   * 封装USB消息（用于发送）
   * @param {Buffer} messageData - 原始消息数据, 必须是 Buffer
   * @returns {Buffer} 封装后的USB消息
   */
  static packUsbMessage(messageData) {
    try {
      const STX = 0x7e;
      const ETX = 0x7d;

      // 计算CRC校验值
      const crc = calculateCrc16(messageData);

      // 创建包含CRC的数据
      const dataWithCrc = Buffer.alloc(messageData.length + 2);
      messageData.copy(dataWithCrc, 0);
      dataWithCrc[messageData.length] = crc & 0xff; // CRC低字节
      dataWithCrc[messageData.length + 1] = (crc >> 8) & 0xff; // CRC高字节

      // 转义数据
      const escapedData = MessageEncoder.escapeUsbData(dataWithCrc);

      // 添加封装壳体
      const packedMessage = Buffer.alloc(escapedData.length + 2);
      packedMessage[0] = STX;
      escapedData.copy(packedMessage, 1);
      packedMessage[packedMessage.length - 1] = ETX;

      console.log(
        `USB message packed: original=${messageData.length}, with CRC=${dataWithCrc.length}, escaped=${escapedData.length}, final=${packedMessage.length}`
      );

      return packedMessage;
    } catch (error) {
      console.error("Error packing USB message:", error);
      return null;
    }
  }
}

module.exports = MessageEncoder;
