<template>
  <v-container fluid class="h-screen">
    <v-row class="h-full">
      <v-col cols="12">
        <v-card class="h-full d-flex flex-column">
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-harddisk</v-icon>
              <span class="text-h6">U盘检测和文件下载演示</span>
            </div>
            <div class="d-flex gap-2">
              <v-chip
                :color="monitoringStatus ? 'success' : 'error'"
                size="small"
              >
                <v-icon start>{{ monitoringStatus ? 'mdi-eye' : 'mdi-eye-off' }}</v-icon>
                {{ monitoringStatus ? '监控中' : '已停止' }}
              </v-chip>
              <v-btn
                :color="monitoringStatus ? 'error' : 'success'"
                :prepend-icon="monitoringStatus ? 'mdi-stop' : 'mdi-play'"
                @click="toggleMonitoring"
                variant="outlined"
                size="small"
              >
                {{ monitoringStatus ? '停止监控' : '开始监控' }}
              </v-btn>
            </div>
          </v-card-title>

          <v-card-text class="flex-grow-1 overflow-hidden">
            <v-row class="h-full">
              <!-- 左侧：设备列表 -->
              <v-col cols="12" md="5" class="h-full">
                <v-card variant="outlined" class="h-full d-flex flex-column">
                  <v-card-title class="d-flex align-center justify-space-between">
                    <span class="text-subtitle-1">存储设备列表</span>
                    <v-btn
                      icon="mdi-refresh"
                      size="small"
                      variant="text"
                      @click="refreshDevices"
                      :loading="refreshing"
                    ></v-btn>
                  </v-card-title>

                  <v-card-text class="flex-grow-1 overflow-y-auto">
                    <div v-if="loading" class="text-center py-8">
                      <v-progress-circular indeterminate color="primary"></v-progress-circular>
                      <div class="mt-2">正在扫描设备...</div>
                    </div>

                    <div v-else-if="devices.length === 0" class="text-center py-8">
                      <v-icon color="warning" size="48">mdi-harddisk-remove</v-icon>
                      <div class="mt-2">未找到存储设备</div>
                      <div class="text-caption text-medium-emphasis mt-1">
                        请插入U盘或检查设备连接
                      </div>
                    </div>

                    <v-list v-else density="compact">
                      <v-list-item
                        v-for="device in devices"
                        :key="device.id"
                        :class="{ 'bg-primary-lighten-5': selectedDevice?.id === device.id }"
                        @click="selectDevice(device)"
                      >
                        <template v-slot:prepend>
                          <v-avatar :color="getDeviceColor(device)" size="32">
                            <v-icon :icon="getDeviceIcon(device)" color="white" size="16"></v-icon>
                          </v-avatar>
                        </template>

                        <v-list-item-title class="text-body-2">
                          {{ device.label }}
                          <v-chip
                            v-if="device.isRemovable"
                            size="x-small"
                            color="primary"
                            class="ml-1"
                          >
                            可移动
                          </v-chip>
                        </v-list-item-title>

                        <v-list-item-subtitle class="text-caption">
                          {{ device.mountPoint }}
                          <br>
                          {{ formatSize(device.freeSpace) }} / {{ formatSize(device.totalSize) }} 可用
                        </v-list-item-subtitle>

                        <template v-slot:append>
                          <v-chip
                            :color="device.isWritable ? 'success' : 'error'"
                            size="x-small"
                          >
                            {{ device.isWritable ? '可写' : '只读' }}
                          </v-chip>
                        </template>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>

              <!-- 右侧：设备详情和演示功能 -->
              <v-col cols="12" md="7" class="h-full">
                <v-card variant="outlined" class="h-full d-flex flex-column">
                  <v-card-title>
                    <span class="text-subtitle-1">设备详情与功能演示</span>
                  </v-card-title>

                  <v-card-text class="flex-grow-1 overflow-y-auto">
                    <!-- 设备详情 -->
                    <div v-if="selectedDevice" class="mb-6">
                      <v-card variant="tonal" class="mb-4">
                        <v-card-text>
                          <v-row>
                            <v-col cols="6">
                              <div class="text-caption text-medium-emphasis">设备名称</div>
                              <div class="text-body-2 font-weight-medium">{{ selectedDevice.label }}</div>
                            </v-col>
                            <v-col cols="6">
                              <div class="text-caption text-medium-emphasis">挂载点</div>
                              <div class="text-body-2 font-weight-medium">{{ selectedDevice.mountPoint }}</div>
                            </v-col>
                            <v-col cols="6">
                              <div class="text-caption text-medium-emphasis">设备类型</div>
                              <div class="text-body-2 font-weight-medium">{{ selectedDevice.type }}</div>
                            </v-col>
                            <v-col cols="6">
                              <div class="text-caption text-medium-emphasis">文件系统</div>
                              <div class="text-body-2 font-weight-medium">{{ selectedDevice.filesystem || 'N/A' }}</div>
                            </v-col>
                          </v-row>

                          <!-- 存储空间使用情况 -->
                          <div class="mt-4">
                            <div class="d-flex justify-space-between text-caption mb-1">
                              <span>存储空间使用情况</span>
                              <span>{{ Math.round((selectedDevice.usedSpace / selectedDevice.totalSize) * 100) }}% 已使用</span>
                            </div>
                            <v-progress-linear
                              :model-value="(selectedDevice.usedSpace / selectedDevice.totalSize) * 100"
                              :color="getUsageColor(selectedDevice)"
                              height="8"
                              rounded
                            ></v-progress-linear>
                            <div class="d-flex justify-space-between text-caption mt-1">
                              <span>已用: {{ formatSize(selectedDevice.usedSpace) }}</span>
                              <span>可用: {{ formatSize(selectedDevice.freeSpace) }}</span>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                    </div>

                    <!-- 功能演示区域 -->
                    <div v-if="!selectedDevice" class="text-center py-8">
                      <v-icon color="info" size="48">mdi-information</v-icon>
                      <div class="mt-2">请选择一个存储设备查看详情</div>
                    </div>

                    <div v-else>
                      <TableDownloadDemo />
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 返回按钮 -->
    <BackButton />

    <!-- 事件日志 -->
    <v-snackbar
      v-model="showEventLog"
      :timeout="3000"
      :color="eventLogColor"
      location="bottom right"
    >
      {{ eventLogMessage }}
      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="showEventLog = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import BackButton from '@/components/BackButton/index.vue';
import TableDownloadDemo from '@/components/TableDownloadDemo/index.vue';

// 响应式数据
const devices = ref([]);
const selectedDevice = ref(null);
const loading = ref(false);
const refreshing = ref(false);
const monitoringStatus = ref(false);
const showEventLog = ref(false);
const eventLogMessage = ref('');
const eventLogColor = ref('info');

// 方法
const loadDevices = async () => {
  loading.value = true;
  try {
    const deviceList = await ipc.invoke('controller/storage/getDevices');
    
    // 为每个设备检查可写状态
    for (const device of deviceList) {
      const writeCheck = await ipc.invoke('controller/storage/checkDeviceWritable', {
        mountPoint: device.mountPoint
      });
      device.isWritable = writeCheck.isWritable;
    }
    
    devices.value = deviceList;
    
    // 获取监控状态
    const status = await ipc.invoke('controller/storage/getMonitoringStatus');
    monitoringStatus.value = status.isMonitoring;
    
  } catch (error) {
    console.error('Failed to load devices:', error);
    showEvent('加载设备列表失败: ' + error.message, 'error');
  } finally {
    loading.value = false;
  }
};

const refreshDevices = async () => {
  refreshing.value = true;
  try {
    await ipc.invoke('controller/storage/refreshDevices');
    await loadDevices();
    showEvent('设备列表已刷新', 'success');
  } catch (error) {
    console.error('Failed to refresh devices:', error);
    showEvent('刷新设备列表失败: ' + error.message, 'error');
  } finally {
    refreshing.value = false;
  }
};

const selectDevice = (device) => {
  selectedDevice.value = device;
};

const toggleMonitoring = async () => {
  try {
    if (monitoringStatus.value) {
      await ipc.invoke('controller/storage/stopMonitoring');
      showEvent('设备监控已停止', 'warning');
    } else {
      await ipc.invoke('controller/storage/startMonitoring');
      showEvent('设备监控已启动', 'success');
    }
    
    // 更新状态
    const status = await ipc.invoke('controller/storage/getMonitoringStatus');
    monitoringStatus.value = status.isMonitoring;
  } catch (error) {
    console.error('Failed to toggle monitoring:', error);
    showEvent('切换监控状态失败: ' + error.message, 'error');
  }
};

const showEvent = (message, color = 'info') => {
  eventLogMessage.value = message;
  eventLogColor.value = color;
  showEventLog.value = true;
};

// 工具方法
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getDeviceIcon = (device) => {
  if (device.isRemovable) {
    return 'mdi-usb-flash-drive';
  }
  if (device.type === 'cdrom') {
    return 'mdi-disc';
  }
  return 'mdi-harddisk';
};

const getDeviceColor = (device) => {
  if (!device.isWritable) {
    return 'error';
  }
  if (device.isRemovable) {
    return 'primary';
  }
  return 'success';
};

const getUsageColor = (device) => {
  const percentage = (device.usedSpace / device.totalSize) * 100;
  if (percentage > 90) return 'error';
  if (percentage > 70) return 'warning';
  return 'success';
};

// 设备事件监听
const setupDeviceListeners = () => {
  ipc.on('storage:device-connected', (event, device) => {
    showEvent(`设备已连接: ${device.label}`, 'success');
    loadDevices();
  });

  ipc.on('storage:device-disconnected', (event, device) => {
    showEvent(`设备已断开: ${device.label}`, 'warning');
    if (selectedDevice.value?.id === device.id) {
      selectedDevice.value = null;
    }
    loadDevices();
  });

  ipc.on('storage:monitoring-started', () => {
    monitoringStatus.value = true;
  });

  ipc.on('storage:monitoring-stopped', () => {
    monitoringStatus.value = false;
  });
};

const removeDeviceListeners = () => {
  ipc.removeAllListeners('storage:device-connected');
  ipc.removeAllListeners('storage:device-disconnected');
  ipc.removeAllListeners('storage:monitoring-started');
  ipc.removeAllListeners('storage:monitoring-stopped');
};

// 生命周期
onMounted(() => {
  setupDeviceListeners();
  loadDevices();
});

onUnmounted(() => {
  removeDeviceListeners();
});
</script>

<style scoped>
.h-screen {
  height: 100vh;
}

.h-full {
  height: 100%;
}
</style>
