<template>
  <v-dialog v-model="dialog" max-width="900px" z-index="900">
    <v-card class="bg-[#333333] text-white rounded-xl">
      <v-card-title class="d-flex justify-space-between align-center">
        <div class="text-h6 text-medium-emphasis ps-2">
          {{ historyType === 'load' ? '最近装车记录' : '最近班组记录' }}
        </div>
        <v-btn icon="mdi-close" variant="text" @click="dialog = false"></v-btn>
      </v-card-title>

      <div class="mx-6 mb-6">
        <div v-if="loading" class="text-center">加载中...</div>
        <div v-else-if="error" class="text-center text-red-500">{{ error }}</div>
        <div v-else>
          <v-data-table
            v-if="historyType === 'load'"
            :headers="loadHeaders"
            :items="historyData"
            fixed-header
            height="400px"
            class="bg-[#444444]"
            hide-default-footer
            :items-per-page="-1"
          >
            <template v-slot:item.timestamp="{ item }">
              {{ dayjs(item.timestamp).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </v-data-table>
          <v-data-table
            v-if="historyType === 'team'"
            :headers="teamHeaders"
            :items="historyData"
            fixed-header
            height="400px"
            class="bg-[#444444]"
            hide-default-footer
            :items-per-page="-1"
          >
            <template v-slot:item.count="{ item }"> {{ item.count }} 车 </template>
            <template v-slot:item.latest_timestamp="{ item }">
              {{ dayjs(item.latest_timestamp).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </v-data-table>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import dayjs from "dayjs";
import { getHistoryLoadApi, getHistoryTeamApi } from "@/api/omsApi";

const loadHeaders = [
  { title: "班组名称", value: "group_name", align: "center", sortable: false },
  { title: "司机名称", value: "driver_name", align: "center", sortable: false },
  { title: "完成时间", value: "timestamp", align: "center", sortable: false },
];

const teamHeaders = [
  { title: "班组名称", value: "group_name", align: "center", sortable: false },
  { title: "装车数量", value: "count", align: "center", sortable: false },
  { title: "最后装车时间", value: "latest_timestamp", align: "center", sortable: false },
];

const props = defineProps({
  vehicleId: String,
  modelValue: Boolean,
  historyType: String, // 'load' or 'team'
});

const emit = defineEmits(["update:modelValue"]);

const dialog = ref(props.modelValue);
const historyData = ref([]);
const loading = ref(false);
const error = ref(null);

const title = computed(() => {
  return props.historyType === "load" ? "最近装车历史记录" : "最近班组历史记录";
});

watch(
  () => props.modelValue,
  (newVal) => {
    dialog.value = newVal;
    if (newVal) {
      fetchHistoryData();
    }
  }
);

watch(dialog, (newVal) => {
  if (!newVal) {
    emit("update:modelValue", false);
  }
});

const fetchHistoryData = async () => {
  loading.value = true;
  error.value = null;
  try {
    if (props.historyType === "load") {
      const { data } = await getHistoryLoadApi(props.vehicleId);
      historyData.value = data.lists;
    } else if (props.historyType === "team") {
      const { data } = await getHistoryTeamApi(props.vehicleId);
      historyData.value = data.lists;
    }
    console.log(historyData.value);
  } catch (err) {
    error.value = "数据加载失败";
    console.error(err);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped></style>
