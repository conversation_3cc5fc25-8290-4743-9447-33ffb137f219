<template>
  <v-dialog v-model="dialog" max-width="900px" z-index="900">
    <v-card class="bg-[#333333] text-white rounded-xl">
      <v-card-title class="d-flex justify-space-between align-center">
        <div class="text-h6 text-medium-emphasis ps-2">补卡</div>
        <v-btn icon="mdi-close" variant="text" @click="dialog = false"></v-btn>
      </v-card-title>

      <v-tabs v-model="currentTab" background-color="#333333" dark>
        <v-tab>故障检修</v-tab>
        <v-tab>装车记录补卡</v-tab>
      </v-tabs>

      <v-window v-model="currentTab">
        <v-window-item :value="0">
          <div class="mx-6 my-6">
            <v-row>
              <v-col cols="4">
                <v-text-field v-model="formData.date" label="日期" type="date" hide-details></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-text-field v-model="formData.startTime" label="开始时间" type="time" hide-details></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-text-field v-model="formData.endTime" label="结束时间" type="time" hide-details></v-text-field>
              </v-col>
              <v-col cols="12">
                <Keyboard v-model="formData.content" label="故障检修内容" hide-details></Keyboard>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
        <v-window-item :value="1">
          <div class="mx-6 my-6 h-[148px]">
            <v-row>
              <v-col cols="6">
                <v-text-field v-model="reissueCardFormData.date" label="日期" type="date" hide-details></v-text-field>
              </v-col>
              <v-col cols="6">
                <v-text-field
                  v-model="reissueCardFormData.time"
                  label="装车时间"
                  type="time"
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" @click="save">保存</v-btn>
        <v-btn color="primary" @click="dialog = false">关闭</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, reactive } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import notificationService from "@/utils/notificationService";

const props = defineProps({
  modelValue: Boolean,
});

const emit = defineEmits(["update:modelValue", "save"]);

const dialog = ref(props.modelValue);
const currentTab = ref(0);

const formData = reactive({
  date: "",
  startTime: "",
  endTime: "",
  content: "",
});

const reissueCardFormData = reactive({
  date: "",
  time: "",
});

watch(
  () => props.modelValue,
  (newVal) => {
    dialog.value = newVal;
  }
);

watch(dialog, (newVal) => {
  if (!newVal) {
    emit("update:modelValue", false);
  }
});

const save = () => {
  if (currentTab.value === 0) {
    if (!formData.date || !formData.startTime || !formData.endTime) {
      notificationService.error("请填写完整信息");
      return;
    }
    emit("save", { type: "troubleshooting", data: formData });
  } else {
    if (!reissueCardFormData.date || !reissueCardFormData.time) {
      notificationService.error("请填写完整信息");
      return;
    }
    emit("save", { type: "reissueCard", data: reissueCardFormData });
  }
  dialog.value = false;
};
</script>

<style scoped></style>
