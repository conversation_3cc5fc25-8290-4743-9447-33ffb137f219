<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span class="text-h6">表格数据导出演示</span>
        <div class="d-flex gap-2">
          <v-btn
            color="primary"
            prepend-icon="mdi-refresh"
            @click="generateSampleData"
            variant="outlined"
          >
            生成示例数据
          </v-btn>
          <v-btn
            color="success"
            prepend-icon="mdi-download"
            @click="showDeviceSelection"
            :disabled="tableData.length === 0"
            variant="elevated"
          >
            导出到设备
          </v-btn>
        </div>
      </v-card-title>

      <v-card-text>
        <!-- 数据统计 -->
        <v-row class="mb-4">
          <v-col cols="12" md="3">
            <v-card variant="outlined">
              <v-card-text class="text-center">
                <div class="text-h4 text-primary">{{ tableData.length }}</div>
                <div class="text-caption">总记录数</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="3">
            <v-card variant="outlined">
              <v-card-text class="text-center">
                <div class="text-h4 text-success">{{ selectedRows.length }}</div>
                <div class="text-caption">已选择</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="3">
            <v-card variant="outlined">
              <v-card-text class="text-center">
                <div class="text-h4 text-info">{{ estimatedFileSize }}</div>
                <div class="text-caption">预估文件大小</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="3">
            <v-card variant="outlined">
              <v-card-text class="text-center">
                <div class="text-h4 text-warning">{{ exportFormat }}</div>
                <div class="text-caption">导出格式</div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <!-- 导出选项 -->
        <v-row class="mb-4">
          <v-col cols="12" md="6">
            <v-select
              v-model="exportFormat"
              :items="exportFormats"
              label="导出格式"
              variant="outlined"
              density="compact"
            ></v-select>
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="fileName"
              label="文件名"
              variant="outlined"
              density="compact"
              :suffix="getFileExtension()"
            ></v-text-field>
          </v-col>
        </v-row>

        <!-- 数据表格 -->
        <v-data-table
          v-model="selectedRows"
          :headers="headers"
          :items="tableData"
          :loading="loading"
          show-select
          item-value="id"
          class="elevation-1"
          :items-per-page="10"
        >
          <template v-slot:item.status="{ item }">
            <v-chip
              :color="getStatusColor(item.status)"
              size="small"
            >
              {{ item.status }}
            </v-chip>
          </template>

          <template v-slot:item.createdAt="{ item }">
            {{ formatDate(item.createdAt) }}
          </template>

          <template v-slot:item.size="{ item }">
            {{ formatFileSize(item.size) }}
          </template>

          <template v-slot:bottom>
            <div class="text-center pa-2">
              <span class="text-caption">
                共 {{ tableData.length }} 条记录，已选择 {{ selectedRows.length }} 条
              </span>
            </div>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>

    <!-- 设备选择对话框 -->
    <DeviceSelectionDialog
      v-model="deviceDialogVisible"
      @device-selected="handleDeviceSelected"
      @cancel="handleDeviceCancel"
    />

    <!-- 导出进度对话框 -->
    <v-dialog v-model="exportDialogVisible" max-width="500" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h6">正在导出数据</span>
        </v-card-title>
        <v-card-text>
          <div class="text-center mb-4">
            <v-progress-circular
              v-if="exportProgress < 100"
              :model-value="exportProgress"
              size="80"
              width="8"
              color="primary"
            >
              {{ Math.round(exportProgress) }}%
            </v-progress-circular>
            <v-icon v-else color="success" size="80">mdi-check-circle</v-icon>
          </div>
          
          <div class="text-center">
            <div class="text-body-1 mb-2">{{ exportStatus }}</div>
            <div class="text-caption text-medium-emphasis">
              目标位置: {{ selectedDevice?.mountPoint }}
            </div>
          </div>

          <v-progress-linear
            :model-value="exportProgress"
            color="primary"
            height="6"
            class="mt-4"
          ></v-progress-linear>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            v-if="exportProgress >= 100"
            color="primary"
            @click="closeExportDialog"
          >
            完成
          </v-btn>
          <v-btn
            v-else
            variant="text"
            @click="cancelExport"
          >
            取消
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import DeviceSelectionDialog from '@/components/DeviceSelectionDialog/index.vue';

// 响应式数据
const tableData = ref([]);
const selectedRows = ref([]);
const loading = ref(false);
const deviceDialogVisible = ref(false);
const exportDialogVisible = ref(false);
const exportProgress = ref(0);
const exportStatus = ref('');
const selectedDevice = ref(null);
const exportFormat = ref('CSV');
const fileName = ref('table_data');

// 导出格式选项
const exportFormats = [
  { title: 'CSV 文件', value: 'CSV' },
  { title: 'Excel 文件', value: 'XLSX' },
  { title: 'JSON 文件', value: 'JSON' },
  { title: 'TXT 文件', value: 'TXT' }
];

// 表格头部定义
const headers = [
  { title: 'ID', value: 'id', align: 'start' },
  { title: '名称', value: 'name', sortable: true },
  { title: '类型', value: 'type', sortable: true },
  { title: '状态', value: 'status', sortable: true },
  { title: '大小', value: 'size', sortable: true },
  { title: '创建时间', value: 'createdAt', sortable: true },
  { title: '描述', value: 'description' }
];

// 计算属性
const estimatedFileSize = computed(() => {
  const dataToExport = selectedRows.value.length > 0 
    ? tableData.value.filter(item => selectedRows.value.includes(item.id))
    : tableData.value;
  
  // 简单估算：每行数据约100字节
  const estimatedBytes = dataToExport.length * 100;
  return formatFileSize(estimatedBytes);
});

// 方法
const generateSampleData = () => {
  loading.value = true;
  
  // 模拟异步数据生成
  setTimeout(() => {
    const sampleData = [];
    const types = ['文档', '图片', '视频', '音频', '其他'];
    const statuses = ['正常', '处理中', '错误', '完成'];
    
    for (let i = 1; i <= 50; i++) {
      sampleData.push({
        id: i,
        name: `数据项_${i.toString().padStart(3, '0')}`,
        type: types[Math.floor(Math.random() * types.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        size: Math.floor(Math.random() * 10000000) + 1000, // 1KB - 10MB
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        description: `这是第${i}个数据项的描述信息`
      });
    }
    
    tableData.value = sampleData;
    selectedRows.value = [];
    loading.value = false;
  }, 1000);
};

const showDeviceSelection = () => {
  deviceDialogVisible.value = true;
};

const handleDeviceSelected = (device) => {
  selectedDevice.value = device;
  startExport();
};

const handleDeviceCancel = () => {
  console.log('用户取消了设备选择');
};

const startExport = async () => {
  exportDialogVisible.value = true;
  exportProgress.value = 0;
  exportStatus.value = '准备导出数据...';

  try {
    // 获取要导出的数据
    const dataToExport = selectedRows.value.length > 0 
      ? tableData.value.filter(item => selectedRows.value.includes(item.id))
      : tableData.value;

    // 模拟导出过程
    await simulateExport(dataToExport);
    
  } catch (error) {
    console.error('导出失败:', error);
    exportStatus.value = '导出失败: ' + error.message;
  }
};

const simulateExport = async (data) => {
  const steps = [
    { progress: 10, status: '正在准备数据...' },
    { progress: 30, status: '正在格式化数据...' },
    { progress: 50, status: '正在生成文件...' },
    { progress: 70, status: '正在写入设备...' },
    { progress: 90, status: '正在验证文件...' },
    { progress: 100, status: '导出完成！' }
  ];

  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 800));
    exportProgress.value = step.progress;
    exportStatus.value = step.status;
  }

  // 模拟实际文件写入
  const fullFileName = `${fileName.value}${getFileExtension()}`;
  console.log(`文件已导出到: ${selectedDevice.value.mountPoint}/${fullFileName}`);
  console.log(`导出格式: ${exportFormat.value}`);
  console.log(`导出数据条数: ${data.length}`);
};

const cancelExport = () => {
  exportDialogVisible.value = false;
  exportProgress.value = 0;
  exportStatus.value = '';
};

const closeExportDialog = () => {
  exportDialogVisible.value = false;
  exportProgress.value = 0;
  exportStatus.value = '';
  selectedDevice.value = null;
};

// 工具方法
const getFileExtension = () => {
  const extensions = {
    'CSV': '.csv',
    'XLSX': '.xlsx',
    'JSON': '.json',
    'TXT': '.txt'
  };
  return extensions[exportFormat.value] || '.csv';
};

const getStatusColor = (status) => {
  const colors = {
    '正常': 'success',
    '处理中': 'warning',
    '错误': 'error',
    '完成': 'info'
  };
  return colors[status] || 'default';
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 生命周期
onMounted(() => {
  generateSampleData();
});
</script>

<style scoped>
.v-data-table {
  border-radius: 8px;
}
</style>
